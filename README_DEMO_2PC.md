# 2PC分布式事务演示系统 - Docker版本

本文档说明如何使用Docker MySQL容器运行2PC分布式事务演示。

## 系统要求

- Python 3.7+
- Docker
- 必要的Python包（见requirements.txt）

## 快速开始

### 方法1: 使用启动脚本（推荐）

```bash
python run_demo_2pc.py
```

### 方法2: 直接运行演示

```bash
python demo_2pc.py
```

### 方法3: 使用主管理脚本

```bash
# 完整设置（包括Docker容器启动和数据库初始化）
python main.py setup

# 然后运行演示
python demo_2pc.py
```

## 系统架构

### Docker容器配置

系统使用两个MySQL容器模拟分布式数据库：

- **mysql1**: 端口3316，数据库db1（银行账户和库存数据）
- **mysql2**: 端口3317，数据库db2（订单和事务日志）

### 演示场景

演示包含以下并发事务：

1. **转账事务1**: 账户2001 → 账户2002，金额500（预期成功）
2. **转账事务2**: 账户2001 → 账户2002，金额600（预期失败，余额不足）
3. **库存事务1**: 订购产品101，数量5（预期成功）
4. **库存事务2**: 订购产品101，数量10（预期失败，库存不足）

## 自动化功能

修改后的`demo_2pc.py`包含以下自动化功能：

### 1. Docker容器管理
- 自动检查Docker是否可用
- 自动检查MySQL容器是否运行
- 自动启动停止的容器
- 自动创建不存在的容器

### 2. 数据库初始化
- 自动初始化数据库结构
- 自动创建演示数据
- 自动验证数据库连接

### 3. 错误处理
- 完善的错误日志记录
- 自动重试机制
- 优雅的异常处理

## 配置文件

### .env文件配置

确保`.env`文件包含正确的Docker容器配置：

```env
# 数据库1配置（Docker容器）
DB1_HOST=localhost
DB1_PORT=3316
DB1_USER=root
DB1_PASSWORD=password
DB1_DATABASE=db1

# 数据库2配置（Docker容器）
DB2_HOST=localhost
DB2_PORT=3317
DB2_USER=root
DB2_PASSWORD=password
DB2_DATABASE=db2
```

## 运行流程

1. **环境检查**: 验证Docker和Python依赖
2. **容器启动**: 自动启动MySQL容器（如需要）
3. **数据库初始化**: 创建表结构和演示数据
4. **并发事务执行**: 运行4个并发事务
5. **结果展示**: 显示事务结果和最终状态
6. **Web界面启动**: 启动Web监控界面（端口5000）

## 监控和日志

- **日志文件**: `logs/distributed_db.log`
- **Web界面**: http://localhost:5000
- **实时监控**: WebSocket事件推送

## 故障排除

### 常见问题

1. **Docker容器启动失败**
   - 检查Docker服务是否运行
   - 检查端口3316和3317是否被占用
   - 查看Docker日志：`docker logs mysql1`

2. **数据库连接失败**
   - 等待容器完全启动（约30秒）
   - 检查防火墙设置
   - 验证.env配置

3. **权限问题**
   - 确保有Docker执行权限
   - 检查文件系统权限

### 手动容器管理

```bash
# 查看容器状态
docker ps -a

# 启动容器
docker start mysql1 mysql2

# 停止容器
docker stop mysql1 mysql2

# 删除容器
docker rm -f mysql1 mysql2

# 查看容器日志
docker logs mysql1
```

## 性能优化

- 容器启动等待时间可在代码中调整（默认30秒）
- 连接池大小可在.env中配置
- 事务超时时间可在.env中配置

## 注意事项

1. 首次运行需要下载MySQL Docker镜像
2. 容器数据在删除容器时会丢失
3. 确保端口3316、3317、5000未被占用
4. 建议在测试环境中运行
