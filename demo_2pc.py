"""
演示2PC事务的正确性
模拟两个并发事务对同一银行账户和库存的操作
展示事务的提交和回滚过程
使用Docker MySQL容器运行
"""

import threading
import time
import subprocess
import sys
from distributed_app import BankingService, InventoryService
from database_manager import get_db_manager
from logger import log_system_info, log_system_error
from init_databases import setup_database_1, setup_database_2
from web_interface import socketio, app  # 导入 Flask 和 SocketIO 实例

# 创建银行服务和库存服务实例
banking_service = None
inventory_service = None

def check_docker():
    """检查Docker是否可用"""
    try:
        result = subprocess.run(['docker', '--version'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            log_system_info("demo_2pc", "Docker 可用")
            return True
        else:
            log_system_error("demo_2pc", "Docker 不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        log_system_error("demo_2pc", "Docker 未安装或不可用")
        return False

def check_mysql_containers():
    """检查MySQL容器是否运行"""
    containers = ['mysql1', 'mysql2']
    running_containers = []

    for container in containers:
        try:
            # 检查容器是否正在运行
            result = subprocess.run(['docker', 'ps', '--filter', f'name={container}', '--format', '{{.Names}}'],
                                  capture_output=True, text=True)
            if container in result.stdout:
                running_containers.append(container)
                log_system_info("demo_2pc", f"容器 {container} 正在运行")
            else:
                log_system_error("demo_2pc", f"容器 {container} 未运行")
        except subprocess.CalledProcessError as e:
            log_system_error("demo_2pc", f"检查容器 {container} 状态失败: {e}")

    return len(running_containers) == len(containers)

def start_mysql_containers():
    """启动MySQL容器"""
    log_system_info("demo_2pc", "正在启动MySQL容器...")

    containers = ['mysql1', 'mysql2']
    for container in containers:
        try:
            # 检查容器是否已存在
            result = subprocess.run(['docker', 'ps', '-a', '--filter', f'name={container}', '--format', '{{.Names}}'],
                                  capture_output=True, text=True)
            if container in result.stdout:
                log_system_info("demo_2pc", f"容器 {container} 已存在，正在启动...")
                subprocess.run(['docker', 'start', container], check=True)
            else:
                log_system_info("demo_2pc", f"创建并启动容器 {container}...")
                if container == 'mysql1':
                    subprocess.run([
                        'docker', 'run', '-d', '--name', 'mysql1',
                        '-e', 'MYSQL_ROOT_PASSWORD=password',
                        '-e', 'MYSQL_DATABASE=db1',
                        '-e', 'MYSQL_USER=dbuser',
                        '-e', 'MYSQL_PASSWORD=dbpass',
                        '-p', '3316:3306',
                        'mysql:8.4'
                    ], check=True)
                else:
                    subprocess.run([
                        'docker', 'run', '-d', '--name', 'mysql2',
                        '-e', 'MYSQL_ROOT_PASSWORD=password',
                        '-e', 'MYSQL_DATABASE=db2',
                        '-e', 'MYSQL_USER=dbuser',
                        '-e', 'MYSQL_PASSWORD=dbpass',
                        '-p', '3317:3306',
                        'mysql:8.4'
                    ], check=True)

            log_system_info("demo_2pc", f"容器 {container} 启动成功")

        except subprocess.CalledProcessError as e:
            log_system_error("demo_2pc", f"启动容器 {container} 失败: {e}")
            return False

    log_system_info("demo_2pc", "等待数据库服务启动...")
    time.sleep(30)  # 等待MySQL完全启动
    return True

def initialize_services():
    """初始化服务实例"""
    global banking_service, inventory_service
    try:
        banking_service = BankingService()
        inventory_service = InventoryService()
        log_system_info("demo_2pc", "服务实例初始化成功")
        return True
    except Exception as e:
        log_system_error("demo_2pc", f"服务实例初始化失败: {e}")
        return False

def transfer_task(from_account, to_account, amount):
    """
    执行转账任务
    :param from_account: 转出账户ID
    :param to_account: 转入账户ID
    :param amount: 转账金额
    """
    try:
        # 执行转账操作
        success = banking_service.transfer_money(from_account, to_account, amount)
        
        if success:
            log_system_info("demo_2pc", f"Transfer succeeded: {from_account} -> {to_account}, Amount: {amount}")
            socketio.emit('transfer_completed', {
                'from_account': from_account,
                'to_account': to_account,
                'amount': amount,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            })
        else:
            log_system_info("demo_2pc", f"Transfer failed: {from_account} -> {to_account}, Amount: {amount}")
            socketio.emit('transfer_failed', {
                'from_account': from_account,
                'to_account': to_account,
                'amount': amount,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            })
    
    except Exception as e:
        log_system_error("demo_2pc", f"Transfer error: {str(e)}")
        socketio.emit('transfer_error', {
            'error': str(e),
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        })

def inventory_task(product_id, quantity, customer_id):
    """
    执行库存任务
    :param product_id: 产品ID
    :param quantity: 订购数量
    :param customer_id: 客户ID
    """
    try:
        # 执行订单处理
        success = inventory_service.process_order(product_id, quantity, customer_id)
        
        if success:
            log_system_info("demo_2pc", f"Order succeeded: Product {product_id}, Quantity {quantity}, Customer {customer_id}")
            socketio.emit('order_completed', {
                'product_id': product_id,
                'quantity': quantity,
                'customer_id': customer_id,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            })
        else:
            log_system_info("demo_2pc", f"Order failed: Product {product_id}, Quantity {quantity}, Customer {customer_id}")
            socketio.emit('order_failed', {
                'product_id': product_id,
                'quantity': quantity,
                'customer_id': customer_id,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            })
    
    except Exception as e:
        log_system_error("demo_2pc", f"Order error: {str(e)}")
        socketio.emit('order_error', {
            'error': str(e),
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        })

def initialize_demo_data():
    """
    初始化演示数据
    """
    try:
        # 检查并启动Docker容器
        if not check_docker():
            log_system_error("demo_2pc", "Docker不可用，无法运行演示")
            return False

        if not check_mysql_containers():
            log_system_info("demo_2pc", "MySQL容器未运行，正在启动...")
            if not start_mysql_containers():
                log_system_error("demo_2pc", "无法启动MySQL容器")
                return False

        # 初始化服务实例
        if not initialize_services():
            log_system_error("demo_2pc", "服务初始化失败")
            return False

        # 初始化数据库1和数据库2
        log_system_info("demo_2pc", "正在初始化数据库...")
        setup_database_1()
        setup_database_2()

        # 确保账户存在并有足够的余额
        log_system_info("demo_2pc", "正在创建演示账户...")
        banking_service.create_account(2001, 1000.0)
        banking_service.create_account(2002, 1500.0)

        # 确保产品存在并有足够的库存
        log_system_info("demo_2pc", "正在创建演示产品...")
        conn = get_db_manager().get_connection('db1')
        cursor = conn.cursor()
        cursor.execute("""
            INSERT IGNORE INTO inventory (product_id, product_name, quantity, price)
            VALUES (101, 'Laptop', 10, 999.99)
        """)
        conn.commit()
        cursor.close()
        conn.close()

        log_system_info("demo_2pc", "演示数据初始化完成")
        return True

    except Exception as e:
        log_system_error("demo_2pc", f"初始化演示数据失败: {e}")
        return False

def run_concurrent_transactions():
    """
    运行并发事务演示
    """
    # 初始化演示数据
    if not initialize_demo_data():
        log_system_error("demo_2pc", "演示数据初始化失败，无法运行演示")
        return False

    log_system_info("demo_2pc", "开始运行并发事务演示...")

    try:
        # 创建线程，模拟两个用户同时操作同一账户和同一产品
        # 转账任务1：从账户2001转500到2002（成功）
        thread1 = threading.Thread(target=transfer_task, args=(2001, 2002, 500))

        # 转账任务2：从账户2001转600到2002（失败，余额不足）
        thread2 = threading.Thread(target=transfer_task, args=(2001, 2002, 600))

        # 库存任务1：订购产品101，数量5（成功）
        thread3 = threading.Thread(target=inventory_task, args=(101, 5, 3001))

        # 库存任务2：订购产品101，数量10（失败，库存不足）
        thread4 = threading.Thread(target=inventory_task, args=(101, 10, 3002))

        # 启动线程
        log_system_info("demo_2pc", "启动并发事务线程...")
        thread1.start()
        thread2.start()
        thread3.start()
        thread4.start()

        # 等待线程完成
        thread1.join()
        thread2.join()
        thread3.join()
        thread4.join()

        # 显示最终账户余额和产品库存
        log_system_info("demo_2pc", "获取最终状态...")
        final_balance_2001 = banking_service.get_account_balance(2001)
        final_balance_2002 = banking_service.get_account_balance(2002)

        conn = get_db_manager().get_connection('db1')
        cursor = conn.cursor()
        cursor.execute("SELECT quantity FROM inventory WHERE product_id = %s", (101,))
        result = cursor.fetchone()
        final_stock_101 = result['quantity'] if result else 0
        cursor.close()
        conn.close()

        log_system_info("demo_2pc", f"Final balance of account 2001: {final_balance_2001}")
        log_system_info("demo_2pc", f"Final balance of account 2002: {final_balance_2002}")
        log_system_info("demo_2pc", f"Final stock of product 101: {final_stock_101}")

        # 发送最终状态到Web界面
        socketio.emit('demo_completed', {
            'account_2001_balance': final_balance_2001,
            'account_2002_balance': final_balance_2002,
            'product_101_stock': final_stock_101,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        })

        log_system_info("demo_2pc", "并发事务演示完成")
        return True

    except Exception as e:
        log_system_error("demo_2pc", f"运行并发事务演示失败: {e}")
        return False

if __name__ == "__main__":
    from database_manager import get_db_manager  # 确保可以访问数据库管理器

    print("=== 2PC分布式事务演示系统 ===")
    print("使用Docker MySQL容器运行")
    print("=" * 50)

    try:
        # 运行演示
        success = run_concurrent_transactions()

        if success:
            print("\n演示运行成功！")
            print("启动Web界面...")

            # 启动 Flask 应用
            log_system_info("demo_2pc", "启动Web界面服务器...")
            socketio.run(app, host='0.0.0.0', port=5000, debug=False)
        else:
            print("\n演示运行失败，请检查日志信息")
            sys.exit(1)

    except KeyboardInterrupt:
        log_system_info("demo_2pc", "用户中断程序")
        print("\n程序被用户中断")
    except Exception as e:
        log_system_error("demo_2pc", f"程序运行异常: {e}")
        print(f"\n程序运行异常: {e}")
        sys.exit(1)